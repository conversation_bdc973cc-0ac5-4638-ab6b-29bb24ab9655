package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// LearnedKnowledge 学习知识结构
type LearnedKnowledge struct {
	ID         int                    `json:"id"`
	Question   string                 `json:"question"`
	Answer     string                 `json:"answer"`
	Category   string                 `json:"category"`
	Keywords   []string               `json:"keywords"`
	Context    string                 `json:"context"`
	Confidence float32                `json:"confidence"`
	Status     string                 `json:"status"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// 直接实现百度热搜知识提取逻辑
func extractBaiduHotSearchKnowledge(content, sourceURL string) *LearnedKnowledge {
	// 解析JSON数据
	var hotSearchData map[string]interface{}
	if err := json.Unmarshal([]byte(content), &hotSearchData); err != nil {
		log.Printf("❌ 解析百度热搜JSON失败: %v", err)
		return nil
	}

	// 检查数据结构
	data, ok := hotSearchData["data"].(map[string]interface{})
	if !ok {
		log.Printf("❌ 百度热搜数据格式错误: 缺少data字段")
		return nil
	}

	cards, ok := data["cards"].([]interface{})
	if !ok || len(cards) == 0 {
		log.Printf("❌ 百度热搜数据格式错误: 缺少cards字段")
		return nil
	}

	// 获取第一个卡片的热搜内容
	firstCard, ok := cards[0].(map[string]interface{})
	if !ok {
		log.Printf("❌ 百度热搜数据格式错误: cards格式错误")
		return nil
	}

	content_data, ok := firstCard["content"].([]interface{})
	if !ok || len(content_data) == 0 {
		log.Printf("❌ 百度热搜数据格式错误: 缺少content字段")
		return nil
	}

	// 提取热搜条目
	var hotTopics []string
	for i, item := range content_data {
		if i >= 5 { // 只取前5个热搜
			break
		}
		
		if itemMap, ok := item.(map[string]interface{}); ok {
			if query, exists := itemMap["query"].(string); exists && query != "" {
				// URL解码
				if decodedQuery, err := url.QueryUnescape(query); err == nil {
					hotTopics = append(hotTopics, decodedQuery)
				} else {
					hotTopics = append(hotTopics, query)
				}
			}
		}
	}

	if len(hotTopics) == 0 {
		log.Printf("❌ 没有提取到有效的热搜话题")
		return nil
	}

	// 生成知识
	question := "当前百度热搜榜上有哪些热门话题？"
	answer := fmt.Sprintf("根据最新的百度热搜榜，当前热门话题包括：\n%s", strings.Join(hotTopics, "\n"))

	knowledge := &LearnedKnowledge{
		Question:   question,
		Answer:     answer,
		Category:   "热搜",
		Keywords:   hotTopics,
		Context:    fmt.Sprintf("从百度热搜API获取: %s", sourceURL),
		Confidence: 0.8, // 热搜数据置信度较高
		Status:     "pending",
		CreatedAt:  time.Now(),
		Metadata: map[string]interface{}{
			"source_url":     sourceURL,
			"content_length": len(content),
			"hot_topics":     hotTopics,
		},
	}

	log.Printf("✅ 成功提取百度热搜知识: %d个话题", len(hotTopics))
	return knowledge
}

// 保存学习知识到数据库
func saveKnowledge(db *sql.DB, knowledge *LearnedKnowledge) error {
	// 将关键词转换为JSON
	keywordsJSON, err := json.Marshal(knowledge.Keywords)
	if err != nil {
		return fmt.Errorf("序列化关键词失败: %v", err)
	}

	// 将元数据转换为JSON
	metadataJSON, err := json.Marshal(knowledge.Metadata)
	if err != nil {
		return fmt.Errorf("序列化元数据失败: %v", err)
	}

	query := `
		INSERT INTO learned_knowledge 
		(question, answer, category, keywords, context, confidence, status, metadata, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`

	_, err = db.Exec(query, 
		knowledge.Question, 
		knowledge.Answer, 
		knowledge.Category, 
		string(keywordsJSON), 
		knowledge.Context, 
		knowledge.Confidence, 
		knowledge.Status, 
		string(metadataJSON))

	return err
}

func main() {
	// 连接数据库
	db, err := sql.Open("mysql", "root:park%123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔧 测试知识提取修复...")

	// 查询一个未处理的百度热搜记录
	query := `
		SELECT id, target_id, title, summary, content, url, category, keywords, crawled_at
		FROM crawl_results 
		WHERE target_id = 7 AND status = 'pending'
		ORDER BY crawled_at DESC 
		LIMIT 1
	`
	
	var id, targetID int
	var title, summary, content, url, category, keywords, crawledAt string
	
	err = db.QueryRow(query).Scan(&id, &targetID, &title, &summary, &content, &url, &category, &keywords, &crawledAt)
	if err != nil {
		fmt.Printf("❌ 查询未处理记录失败: %v\n", err)
		return
	}

	fmt.Printf("📋 找到未处理记录: ID=%d, URL=%s\n", id, url)
	fmt.Printf("内容长度: %d\n", len(content))

	// 测试知识提取
	fmt.Println("\n🔍 测试知识提取...")
	knowledge := extractBaiduHotSearchKnowledge(content, url)
	
	if knowledge == nil {
		fmt.Println("❌ 知识提取失败")
		return
	}

	fmt.Println("✅ 知识提取成功!")
	fmt.Printf("问题: %s\n", knowledge.Question)
	fmt.Printf("答案: %s\n", knowledge.Answer)
	fmt.Printf("分类: %s\n", knowledge.Category)
	fmt.Printf("关键词: %v\n", knowledge.Keywords)
	fmt.Printf("置信度: %.2f\n", knowledge.Confidence)

	// 保存学习知识
	fmt.Println("\n💾 保存学习知识...")
	err = saveKnowledge(db, knowledge)
	if err != nil {
		fmt.Printf("❌ 保存学习知识失败: %v\n", err)
		return
	}

	fmt.Println("✅ 学习知识保存成功!")

	// 更新爬取结果状态
	fmt.Println("\n🔄 更新爬取结果状态...")
	updateQuery := "UPDATE crawl_results SET status = 'processed', processed_at = NOW() WHERE id = ?"
	_, err = db.Exec(updateQuery, id)
	if err != nil {
		fmt.Printf("❌ 更新状态失败: %v\n", err)
		return
	}

	fmt.Println("✅ 爬取结果状态更新成功!")

	fmt.Println("\n🎉 测试完成!")
}
